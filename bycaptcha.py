import cv2
import os
import numpy as np
import pyautogui
import time

# --- <PERSON><PERSON><PERSON> hình ---
SS_FOLDER = 'Ss'  # Th<PERSON> mục chứa các thư mục ký tự mẫu
TEST_IMG = 'Ss/a/a (1).png'  # Ảnh cần nhận diện (ví dụ)
CLICK_POS = (500, 300)  # Tọa độ cần click (ví dụ)

# --- <PERSON><PERSON><PERSON> tải mẫu ký tự ---
def load_templates(ss_folder):
    templates = {}
    for label in os.listdir(ss_folder):
        label_folder = os.path.join(ss_folder, label)
        if os.path.isdir(label_folder):
            for img_name in os.listdir(label_folder):
                img_path = os.path.join(label_folder, img_name)
                img = cv2.imread(img_path, 0)
                if img is not None:
                    templates.setdefault(label, []).append(img)
    return templates

# --- <PERSON><PERSON><PERSON> nhận diện ký tự bằng so khớp mẫu ---
def recognize_char(img_path, templates):
    img = cv2.imread(img_path, 0)
    best_score = -1
    best_label = None
    for label, imgs in templates.items():
        for template in imgs:
            if img.shape == template.shape:
                res = cv2.matchTemplate(img, template, cv2.TM_CCOEFF_NORMED)
                score = res[0][0]
                if score > best_score:
                    best_score = score
                    best_label = label
    return best_label, best_score

# --- Nhận diện ký tự ---
def main():
    print('Đang tải mẫu ký tự...')
    templates = load_templates(SS_FOLDER)
    print('Đang nhận diện ký tự trong ảnh:', TEST_IMG)
    label, score = recognize_char(TEST_IMG, templates)
    print(f'Ký tự nhận diện được: {label} (độ khớp: {score})')

    # --- Tự động click vào vị trí đã cấu hình ---
    print(f'Chuyển sang cửa sổ captcha, sẽ click vào vị trí {CLICK_POS} sau 3 giây...')
    time.sleep(3)
    pyautogui.moveTo(CLICK_POS[0], CLICK_POS[1], duration=0.5)
    pyautogui.click()
    print(f'Đã click vào vị trí {CLICK_POS}')

if __name__ == '__main__':
    main() 