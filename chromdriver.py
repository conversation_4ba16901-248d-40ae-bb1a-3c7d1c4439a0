import requests
import zipfile
import io
import os

def get_chrome_version():
    import winreg
    reg_path = r"SOFTWARE\\Google\\Chrome\\BLBeacon"
    try:
        reg_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path)
        version, _ = winreg.QueryValueEx(reg_key, "version")
        return version
    except Exception:
        return None

def get_latest_chromedriver_version(chrome_version):
    major_version = chrome_version.split('.')[0]
    url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
    response = requests.get(url)
    return response.text.strip()

def download_and_extract_chromedriver(version, extract_to='.'):
    url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip"
    response = requests.get(url)
    with zipfile.ZipFile(io.BytesIO(response.content)) as zip_ref:
        zip_ref.extractall(extract_to)
    print(f"Downloaded and extracted chromedriver {version} to {extract_to}")

if __name__ == '__main__':
    chrome_version = get_chrome_version()
    if not chrome_version:
        print("Không tìm thấy Chrome trên máy.")
    else:
        print(f"Chrome version: {chrome_version}")
        driver_version = get_latest_chromedriver_version(chrome_version)
        print(f"Downloading ChromeDriver version: {driver_version}")
        download_and_extract_chromedriver(driver_version)
