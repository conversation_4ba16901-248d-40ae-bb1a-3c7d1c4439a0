@echo off
chcp 65001 >nul
title TikTok Automation - Samsung J7 Prime

echo.
echo ========================================
echo 🔧 TikTok Automation cho J7 Prime
echo ========================================
echo.

echo 📱 Kiểm tra Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python chưa được cài đặt hoặc không có trong PATH
    echo Vui lòng cài đặt Python và thêm vào PATH
    pause
    exit /b 1
)

echo ✅ Python đã sẵn sàng
echo.

echo 📋 Chọn chế độ:
echo 1. Chạy automation (có tùy chọn Google login)
echo 2. Xem tips tối ưu
echo 3. Thoát
echo.

set /p choice="Nhập lựa chọn (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🚀 Khởi động automation...
    python run_j7prime.py
) else if "%choice%"=="2" (
    echo.
    python run_j7prime.py --tips
) else if "%choice%"=="3" (
    echo 👋 Tạm biệt!
    exit /b 0
) else (
    echo ❌ Lựa chọn không hợp lệ
)

echo.
pause
