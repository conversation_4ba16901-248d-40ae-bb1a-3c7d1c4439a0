#!/usr/bin/env python3
"""
Script đặc biệt để chạy TikTok automation trên Samsung J7 Prime
Tối ưu hóa cho thiết bị có hiệu suất thấp và lag
"""

import sys
import time
import logging
from tiktok_reg_adb import TikTokAutomation

# Setup logging đặc biệt cho J7 Prime
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('j7prime_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_banner():
    """In banner thông tin"""
    print("=" * 60)
    print("🔧 TikTok Automation - Tối ưu cho Samsung J7 Prime")
    print("=" * 60)
    print("📱 Thiết bị: Samsung Galaxy J7 Prime")
    print("⚡ Chế độ: Tối ưu hiệu suất thấp")
    print("🐌 Timing: Conservative (chậm nhưng ổn định)")
    print("=" * 60)

def check_j7prime_requirements():
    """Kiểm tra yêu cầu cho J7 Prime"""
    logger.info("🔍 Kiểm tra yêu cầu cho J7 Prime...")
    
    requirements = [
        "✅ USB Debugging đã bật",
        "✅ Đã cài đặt ADB drivers",
        "✅ Kết nối USB ổn định",
        "✅ TikTok đã được cài đặt",
        "⚠️ Đóng tất cả app khác để giảm lag",
        "⚠️ Sạc pin > 50% để tránh throttling",
        "⚠️ Không sử dụng điện thoại trong quá trình chạy"
    ]
    
    for req in requirements:
        print(req)
        time.sleep(0.5)
    
    print("\n" + "=" * 60)
    response = input("Bạn đã sẵn sàng? (y/n): ").lower().strip()
    return response in ['y', 'yes', 'có']

def run_j7prime_automation():
    """Chạy automation với cấu hình tối ưu cho J7 Prime"""
    try:
        print_banner()
        
        if not check_j7prime_requirements():
            print("❌ Hủy bỏ. Vui lòng chuẩn bị đầy đủ trước khi chạy.")
            return
        
        logger.info("🚀 Khởi tạo automation cho J7 Prime...")
        
        # Sử dụng config đặc biệt cho J7 Prime
        automation = TikTokAutomation("config_j7prime.json")
        
        logger.info("📱 Kiểm tra kết nối thiết bị...")
        if not automation.check_device_connection():
            logger.error("❌ Không thể kết nối với thiết bị")
            return
        
        logger.info("⚡ Tối ưu hiệu suất thiết bị...")
        automation.optimize_device_performance()
        
        logger.info("⏳ Chờ thiết bị ổn định...")
        time.sleep(10)
        
        # Menu lựa chọn
        print("\n" + "=" * 50)
        print("Chọn chế độ chạy:")
        print("1. Quy trình cơ bản (đến màn hình đăng ký)")
        print("2. Đăng ký một tài khoản (username/password)")
        print("3. Đăng ký bằng Google account")
        print("4. Đăng ký nhiều tài khoản")
        print("=" * 50)

        choice = input("Nhập lựa chọn (1-4): ").strip()
        
        if choice == "1":
            logger.info("🎯 Chạy quy trình cơ bản...")
            success = automation.run_registration_flow()
            
        elif choice == "2":
            username = input("Nhập username/email/số điện thoại: ").strip()
            password = input("Nhập password: ").strip()
            
            if not username or not password:
                logger.error("❌ Username và password không được để trống")
                return
            
            logger.info(f"🎯 Đăng ký tài khoản: {username}")
            success = automation.register_account(username, password)
            
        elif choice == "3":
            print("\n🔐 Đăng ký bằng Google Account")
            print("Lưu ý: Bạn có thể để trống email/password để đăng ký thủ công")

            email = input("Nhập email Google (hoặc Enter để bỏ qua): ").strip()
            password = ""

            if email:
                password = input("Nhập password Google: ").strip()

            logger.info("🎯 Đăng ký bằng Google account...")
            if email and password:
                success = automation.register_with_google(email, password)
            else:
                # Chỉ chạy đến bước chọn Google, người dùng tự đăng nhập
                success = automation.run_registration_flow()

        elif choice == "4":
            start_index = input("Bắt đầu từ index (mặc định 0): ").strip()
            max_accounts = input("Số lượng tối đa (Enter để đăng ký tất cả): ").strip()

            start_index = int(start_index) if start_index else 0
            max_accounts = int(max_accounts) if max_accounts else None

            logger.info(f"🎯 Đăng ký nhiều tài khoản từ index {start_index}")
            results = automation.register_multiple_accounts(
                start_index=start_index,
                max_accounts=max_accounts
            )

            # Thống kê kết quả
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            logger.info(f"📊 Kết quả: {successful}/{total} tài khoản thành công")
            success = successful > 0
            
        else:
            logger.error("❌ Lựa chọn không hợp lệ")
            return
        
        if success:
            logger.info("🎉 Hoàn thành thành công!")
        else:
            logger.error("❌ Có lỗi xảy ra trong quá trình thực hiện")
            
    except KeyboardInterrupt:
        logger.info("⏹️ Người dùng dừng chương trình")
    except Exception as e:
        logger.error(f"❌ Lỗi không mong muốn: {e}")
    finally:
        logger.info("🏁 Kết thúc automation")

def show_tips():
    """Hiển thị tips tối ưu cho J7 Prime"""
    tips = [
        "💡 TIPS TỐI ƯU CHO J7 PRIME:",
        "",
        "1. 🔋 Sạc pin đầy trước khi chạy",
        "2. 📱 Đóng tất cả app khác",
        "3. 🌡️ Để điện thoại ở nơi thoáng mát",
        "4. 🔇 Tắt âm thanh và rung",
        "5. ✈️ Bật chế độ máy bay rồi bật lại WiFi",
        "6. 🧹 Xóa cache trước khi chạy",
        "7. ⏰ Chạy vào lúc điện thoại ít lag nhất",
        "8. 🔄 Khởi động lại điện thoại nếu quá lag",
        "",
        "⚠️ LƯU Ý: J7 Prime là thiết bị cũ, cần kiên nhẫn!"
    ]
    
    for tip in tips:
        print(tip)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--tips":
        show_tips()
    else:
        run_j7prime_automation()
