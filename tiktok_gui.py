import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import os
from tiktok_reg_adb import TikTokAutomation
import logging

class TikTokGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("TikTok Registration Automation")
        self.root.geometry("800x600")
        
        # Queue để giao tiế<PERSON> gi<PERSON>a threads
        self.log_queue = queue.Queue()
        
        # Automation instance
        self.automation = None
        
        # Setup GUI
        self.setup_gui()
        
        # Setup logging để hiển thị trong GUI
        self.setup_logging()
        
        # Bắt đầu check log queue
        self.check_log_queue()
    
    def setup_gui(self):
        """Thiết lập giao diện"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="TikTok Registration Automation", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Config section
        config_frame = ttk.LabelFrame(main_frame, text="Cấu hình", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # Config file
        ttk.Label(config_frame, text="File config:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.config_var = tk.StringVar(value="config.json")
        config_entry = ttk.Entry(config_frame, textvariable=self.config_var)
        config_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(config_frame, text="Chọn", command=self.browse_config).grid(row=0, column=2)
        
        # Accounts file
        ttk.Label(config_frame, text="File tài khoản:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.accounts_var = tk.StringVar(value="accounts.txt")
        accounts_entry = ttk.Entry(config_frame, textvariable=self.accounts_var)
        accounts_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(config_frame, text="Chọn", command=self.browse_accounts).grid(row=1, column=2)
        
        # Mode selection
        mode_frame = ttk.LabelFrame(main_frame, text="Chế độ hoạt động", padding="10")
        mode_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.mode_var = tk.StringVar(value="basic")
        ttk.Radiobutton(mode_frame, text="Quy trình cơ bản", variable=self.mode_var,
                       value="basic").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="Đăng ký một tài khoản", variable=self.mode_var,
                       value="single").grid(row=0, column=1, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="Đăng ký bằng Google", variable=self.mode_var,
                       value="google").grid(row=1, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="Đăng ký nhiều tài khoản", variable=self.mode_var,
                       value="multiple").grid(row=1, column=1, sticky=tk.W)
        
        # Single account section
        single_frame = ttk.LabelFrame(main_frame, text="Đăng ký một tài khoản", padding="10")
        single_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        single_frame.columnconfigure(1, weight=1)
        
        ttk.Label(single_frame, text="Username:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.username_var = tk.StringVar()
        ttk.Entry(single_frame, textvariable=self.username_var).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(single_frame, text="Password:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.password_var = tk.StringVar()
        ttk.Entry(single_frame, textvariable=self.password_var, show="*").grid(row=1, column=1, sticky=(tk.W, tk.E))

        # Google account section
        google_frame = ttk.LabelFrame(main_frame, text="Đăng ký bằng Google", padding="10")
        google_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        google_frame.columnconfigure(1, weight=1)

        ttk.Label(google_frame, text="Email Google:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.google_email_var = tk.StringVar()
        ttk.Entry(google_frame, textvariable=self.google_email_var).grid(row=0, column=1, sticky=(tk.W, tk.E))

        ttk.Label(google_frame, text="Password Google:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.google_password_var = tk.StringVar()
        ttk.Entry(google_frame, textvariable=self.google_password_var, show="*").grid(row=1, column=1, sticky=(tk.W, tk.E))

        ttk.Label(google_frame, text="(Để trống để đăng nhập thủ công)", font=("Arial", 8)).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        # Multiple accounts section
        multiple_frame = ttk.LabelFrame(main_frame, text="Đăng ký nhiều tài khoản", padding="10")
        multiple_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        multiple_frame.columnconfigure(1, weight=1)
        
        ttk.Label(multiple_frame, text="Bắt đầu từ index:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.start_index_var = tk.StringVar(value="0")
        ttk.Entry(multiple_frame, textvariable=self.start_index_var, width=10).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(multiple_frame, text="Số lượng tối đa:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.max_accounts_var = tk.StringVar()
        ttk.Entry(multiple_frame, textvariable=self.max_accounts_var, width=10).grid(row=0, column=3, sticky=tk.W)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=(10, 0))
        
        self.start_button = ttk.Button(button_frame, text="Bắt đầu", command=self.start_automation)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="Dừng", command=self.stop_automation, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Xóa log", command=self.clear_log).pack(side=tk.LEFT)
        
        # Log display
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def setup_logging(self):
        """Thiết lập logging để hiển thị trong GUI"""
        class GUILogHandler(logging.Handler):
            def __init__(self, log_queue):
                super().__init__()
                self.log_queue = log_queue
            
            def emit(self, record):
                self.log_queue.put(self.format(record))
        
        # Thêm handler cho GUI
        gui_handler = GUILogHandler(self.log_queue)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        
        # Lấy logger từ tiktok_reg_adb
        logger = logging.getLogger('tiktok_reg_adb')
        logger.addHandler(gui_handler)
    
    def check_log_queue(self):
        """Kiểm tra và hiển thị log từ queue"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_text.config(state=tk.NORMAL)
                self.log_text.insert(tk.END, message + "\n")
                self.log_text.see(tk.END)
                self.log_text.config(state=tk.DISABLED)
        except queue.Empty:
            pass
        
        # Lặp lại sau 100ms
        self.root.after(100, self.check_log_queue)
    
    def browse_config(self):
        """Chọn file config"""
        filename = filedialog.askopenfilename(
            title="Chọn file config",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.config_var.set(filename)
    
    def browse_accounts(self):
        """Chọn file tài khoản"""
        filename = filedialog.askopenfilename(
            title="Chọn file tài khoản",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.accounts_var.set(filename)
    
    def clear_log(self):
        """Xóa log"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def start_automation(self):
        """Bắt đầu automation"""
        try:
            # Validate inputs
            if not os.path.exists(self.config_var.get()):
                messagebox.showerror("Lỗi", f"File config không tồn tại: {self.config_var.get()}")
                return
            
            mode = self.mode_var.get()
            
            if mode == "single":
                if not self.username_var.get() or not self.password_var.get():
                    messagebox.showerror("Lỗi", "Vui lòng nhập username và password")
                    return

            elif mode == "google":
                # Google mode không cần validate vì có thể đăng nhập thủ công
                pass
            
            elif mode == "multiple":
                if not os.path.exists(self.accounts_var.get()):
                    messagebox.showerror("Lỗi", f"File tài khoản không tồn tại: {self.accounts_var.get()}")
                    return
            
            # Disable start button, enable stop button
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            
            # Start automation in separate thread
            self.automation_thread = threading.Thread(target=self.run_automation, daemon=True)
            self.automation_thread.start()
            
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể bắt đầu automation: {e}")
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def run_automation(self):
        """Chạy automation trong thread riêng"""
        try:
            # Khởi tạo automation
            self.automation = TikTokAutomation(self.config_var.get())
            
            mode = self.mode_var.get()
            
            if mode == "basic":
                self.automation.run_registration_flow()
            
            elif mode == "single":
                self.automation.register_account(
                    self.username_var.get(),
                    self.password_var.get()
                )

            elif mode == "google":
                email = self.google_email_var.get().strip()
                password = self.google_password_var.get().strip()

                if email and password:
                    self.automation.register_with_google(email, password)
                else:
                    # Chỉ chạy đến bước chọn Google, người dùng tự đăng nhập
                    self.automation.run_registration_flow()
            
            elif mode == "multiple":
                start_index = int(self.start_index_var.get()) if self.start_index_var.get() else 0
                max_accounts = int(self.max_accounts_var.get()) if self.max_accounts_var.get() else None
                
                self.automation.register_multiple_accounts(
                    accounts_file=self.accounts_var.get(),
                    start_index=start_index,
                    max_accounts=max_accounts
                )
            
        except Exception as e:
            self.log_queue.put(f"ERROR - Lỗi trong automation: {e}")
        
        finally:
            # Re-enable start button, disable stop button
            self.root.after(0, lambda: (
                self.start_button.config(state=tk.NORMAL),
                self.stop_button.config(state=tk.DISABLED)
            ))
    
    def stop_automation(self):
        """Dừng automation"""
        # Hiện tại chỉ disable button, có thể thêm logic dừng automation
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.log_queue.put("INFO - Đã yêu cầu dừng automation")


def main():
    root = tk.Tk()
    app = TikTokGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
