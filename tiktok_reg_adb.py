import subprocess
import time

ADB_PATH = r"C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools\adb.exe"

def clear_tiktok_data():
    subprocess.run([ADB_PATH, 'shell', 'pm', 'clear', 'com.zhiliaoapp.musically'], check=True)
    time.sleep(2)

def open_tiktok():
    subprocess.run([ADB_PATH, 'shell', 'monkey', '-p', 'com.zhiliaoapp.musically', '-c', 'android.intent.category.LAUNCHER', '1'], check=True)
    time.sleep(1)

def tap(x, y, delay=1.5):
    subprocess.run([ADB_PATH, 'shell', 'input', 'tap', str(x), str(y)], check=True)
    time.sleep(delay)

def input_text(text, delay=1.5):
    safe_text = text.replace(' ', '%s')
    subprocess.run([ADB_PATH, 'shell', 'input', 'text', safe_text], check=True)
    time.sleep(delay)

def clear_input(repeat=40,delay=1.0):
    for _ in range(repeat):
        subprocess.run([ADB_PATH, 'shell', 'input', 'keyevent', '67'])
    time.sleep(delay)

if __name__ == "__main__":
    clear_tiktok_data()
    open_tiktok()
    time.sleep(40)
    tap(540, 1800)  # Agree and continue
    tap(200, 1850)  # Skip
    time.sleep(15)  # Chờ 15 giây sau khi skip để app load
    tap(950, 1850)  # Chuyển sang tab Profile
    time.sleep(5)   # Chờ 5 giây
    tap(850, 1900)  #singup
    time.sleep(5)   # Chờ 5 giây
    tap(540, 1100)
