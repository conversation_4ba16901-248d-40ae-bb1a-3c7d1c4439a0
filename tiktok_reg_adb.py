import subprocess
import time
import logging
import os
import json
from typing import Optional, Dict, Any
from datetime import datetime

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tiktok_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TikTokAutomationError(Exception):
    """Custom exception cho TikTok automation"""
    pass

class TikTokAutomation:
    def __init__(self, config_file: str = "config.json"):
        """
        Khởi tạo TikTok Automation

        Args:
            config_file: Đường dẫn đến file config
        """
        self.config = self._load_config(config_file)
        self.adb_path = self.config.get('adb_path',
            r"C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools\adb.exe")
        self.package_name = self.config.get('package_name', 'com.zhiliaoapp.musically')
        self.coordinates = self.config.get('coordinates', {})
        self.delays = self.config.get('delays', {})

        # Kiểm tra ADB có tồn tại không
        if not os.path.exists(self.adb_path):
            raise TikTokAutomationError(f"ADB không tìm thấy tại: {self.adb_path}")

        logger.info("TikTok Automation đã được khởi tạo")

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load config từ file JSON"""
        default_config = {
            "adb_path": r"C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools\adb.exe",
            "package_name": "com.zhiliaoapp.musically",
            "coordinates": {
                "agree_continue": [540, 1800],
                "skip": [200, 1850],
                "profile_tab": [950, 1850],
                "signup": [850, 1900],
                "phone_input": [540, 1100]
            },
            "delays": {
                "app_load": 40,
                "after_skip": 15,
                "general": 5,
                "tap": 1.5,
                "input": 1.5,
                "clear": 1.0
            }
        }

        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    logger.info(f"Đã load config từ {config_file}")
            else:
                # Tạo file config mặc định
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                logger.info(f"Đã tạo file config mặc định: {config_file}")
        except Exception as e:
            logger.warning(f"Lỗi khi load config: {e}. Sử dụng config mặc định.")

        return default_config

    def _run_adb_command(self, command: list, timeout: int = 45) -> bool:
        """
        Chạy lệnh ADB với error handling và tối ưu cho thiết bị lag

        Args:
            command: List các tham số lệnh ADB
            timeout: Timeout cho lệnh (giây) - tăng cho thiết bị lag

        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            # Tăng timeout cho thiết bị lag
            if self.config.get('performance', {}).get('device_type') == 'low_end':
                timeout = int(timeout * 1.5)

            full_command = [self.adb_path] + command

            # Giảm logging cho thiết bị lag
            if not self.config.get('performance', {}).get('reduce_logging', False):
                logger.debug(f"Chạy lệnh: {' '.join(full_command)}")

            result = subprocess.run(
                full_command,
                check=True,
                timeout=timeout,
                capture_output=True,
                text=True
            )

            if result.stdout and not self.config.get('performance', {}).get('reduce_logging', False):
                logger.debug(f"Output: {result.stdout.strip()}")

            # Thêm delay nhỏ cho thiết bị lag
            if self.config.get('settings', {}).get('low_performance_mode', False):
                time.sleep(0.5)

            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Lệnh ADB thất bại: {e}")
            if e.stderr:
                logger.error(f"Error output: {e.stderr}")
            return False
        except subprocess.TimeoutExpired:
            logger.error(f"Lệnh ADB timeout sau {timeout} giây")
            return False
        except Exception as e:
            logger.error(f"Lỗi không mong muốn: {e}")
            return False

    def check_device_connection(self) -> bool:
        """Kiểm tra kết nối với device"""
        logger.info("Kiểm tra kết nối device...")
        return self._run_adb_command(['devices'])

    def optimize_device_performance(self) -> bool:
        """Tối ưu hiệu suất thiết bị cho J7 Prime lag"""
        logger.info("Tối ưu hiệu suất thiết bị...")

        try:
            # Tắt animation để giảm lag
            commands = [
                ['shell', 'settings', 'put', 'global', 'window_animation_scale', '0'],
                ['shell', 'settings', 'put', 'global', 'transition_animation_scale', '0'],
                ['shell', 'settings', 'put', 'global', 'animator_duration_scale', '0'],
                # Giảm background processes
                ['shell', 'am', 'kill-all'],
                # Clear cache
                ['shell', 'pm', 'trim-caches', '1000M']
            ]

            success_count = 0
            for cmd in commands:
                if self._run_adb_command(cmd):
                    success_count += 1
                time.sleep(1)  # Delay giữa các lệnh

            logger.info(f"Đã thực hiện {success_count}/{len(commands)} lệnh tối ưu")

            # Chờ thiết bị ổn định
            time.sleep(5)
            return success_count > len(commands) // 2

        except Exception as e:
            logger.error(f"Lỗi khi tối ưu thiết bị: {e}")
            return False

    def clear_tiktok_data(self) -> bool:
        """Xóa dữ liệu TikTok"""
        logger.info("Xóa dữ liệu TikTok...")
        success = self._run_adb_command(['shell', 'pm', 'clear', self.package_name])
        if success:
            time.sleep(self.delays.get('general', 2))
            logger.info("Đã xóa dữ liệu TikTok thành công")
        else:
            logger.error("Không thể xóa dữ liệu TikTok")
        return success

    def open_tiktok(self) -> bool:
        """Mở ứng dụng TikTok"""
        logger.info("Mở ứng dụng TikTok...")
        success = self._run_adb_command([
            'shell', 'monkey', '-p', self.package_name,
            '-c', 'android.intent.category.LAUNCHER', '1'
        ])
        if success:
            time.sleep(self.delays.get('general', 1))
            logger.info("Đã mở TikTok thành công")
        else:
            logger.error("Không thể mở TikTok")
        return success

    def tap(self, x: int, y: int, delay: Optional[float] = None) -> bool:
        """
        Tap tại tọa độ x, y với tối ưu cho thiết bị lag

        Args:
            x: Tọa độ x
            y: Tọa độ y
            delay: Thời gian chờ sau khi tap (giây)
        """
        if delay is None:
            delay = self.delays.get('tap', 1.5)
            # Tăng delay cho thiết bị lag
            if self.config.get('performance', {}).get('device_type') == 'low_end':
                delay *= self.config.get('performance', {}).get('cpu_wait_multiplier', 2.0)

        logger.info(f"Tap tại ({x}, {y})")

        # Thêm delay trước khi tap cho thiết bị lag
        if self.config.get('settings', {}).get('low_performance_mode', False):
            time.sleep(self.delays.get('device_response', 2.0))

        success = self._run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
        if success:
            time.sleep(delay)
            logger.info(f"✅ Tap thành công tại ({x}, {y})")
        else:
            logger.error(f"❌ Không thể tap tại ({x}, {y})")
        return success

    def input_text(self, text: str, delay: Optional[float] = None) -> bool:
        """
        Nhập text với tối ưu cho thiết bị lag

        Args:
            text: Text cần nhập
            delay: Thời gian chờ sau khi nhập
        """
        if delay is None:
            delay = self.delays.get('input', 1.5)
            # Tăng delay cho thiết bị lag
            if self.config.get('performance', {}).get('device_type') == 'low_end':
                delay *= self.config.get('performance', {}).get('cpu_wait_multiplier', 2.0)

        # Xử lý ký tự đặc biệt
        safe_text = text.replace(' ', '%s').replace('&', '\\&').replace('(', '\\(').replace(')', '\\)')
        logger.info(f"Nhập text: {text}")

        # Thêm delay trước khi nhập cho thiết bị lag
        if self.config.get('settings', {}).get('low_performance_mode', False):
            time.sleep(self.delays.get('device_response', 2.0))

        success = self._run_adb_command(['shell', 'input', 'text', safe_text])
        if success:
            time.sleep(delay)
            logger.info(f"✅ Nhập text thành công: {text}")
        else:
            logger.error(f"❌ Không thể nhập text: {text}")
        return success

    def clear_input(self, repeat: int = 40, delay: Optional[float] = None) -> bool:
        """
        Xóa input bằng cách nhấn backspace

        Args:
            repeat: Số lần nhấn backspace
            delay: Thời gian chờ sau khi xóa
        """
        if delay is None:
            delay = self.delays.get('clear', 1.0)

        logger.info(f"Xóa input ({repeat} lần)")
        success = True
        for i in range(repeat):
            if not self._run_adb_command(['shell', 'input', 'keyevent', '67']):
                success = False
                logger.error(f"Lỗi khi xóa input lần {i+1}")
                break

        if success:
            time.sleep(delay)
        return success

    def take_screenshot(self, filename: Optional[str] = None, description: str = "") -> bool:
        """
        Chụp ảnh màn hình với tính năng tự động tạo thư mục

        Args:
            filename: Tên file ảnh (nếu None sẽ tự động tạo)
            description: Mô tả cho ảnh chụp
        """
        # Kiểm tra setting có cho phép chụp ảnh không
        if not self.config.get('settings', {}).get('take_screenshots', True):
            return True  # Return True để không làm gián đoạn flow

        # Tạo thư mục screenshots nếu chưa có
        screenshot_dir = self.config.get('settings', {}).get('screenshot_dir', 'screenshots')
        if not os.path.exists(screenshot_dir):
            os.makedirs(screenshot_dir)
            logger.info(f"Đã tạo thư mục: {screenshot_dir}")

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

        # Đảm bảo filename có đường dẫn đầy đủ
        if not os.path.dirname(filename):
            filename = os.path.join(screenshot_dir, filename)

        logger.info(f"Chụp ảnh màn hình: {filename}" + (f" - {description}" if description else ""))

        try:
            # Chụp ảnh trên device
            if not self._run_adb_command(['shell', 'screencap', '/sdcard/screenshot_temp.png']):
                return False

            # Pull ảnh về máy tính
            success = self._run_adb_command(['pull', '/sdcard/screenshot_temp.png', filename])
            if success:
                logger.info(f"Đã lưu ảnh: {filename}")
                # Xóa file tạm trên device
                self._run_adb_command(['shell', 'rm', '/sdcard/screenshot_temp.png'])
            else:
                logger.error("Không thể lưu ảnh màn hình")

            return success

        except Exception as e:
            logger.error(f"Lỗi khi chụp ảnh màn hình: {e}")
            return False

    def verify_element_exists(self, element_description: str, timeout: int = 10) -> bool:
        """
        Verify xem một element có tồn tại trên màn hình không bằng cách chụp ảnh

        Args:
            element_description: Mô tả element cần tìm
            timeout: Thời gian chờ tối đa (giây) - hiện tại chưa sử dụng

        Returns:
            True nếu element được tìm thấy (hiện tại chỉ chụp ảnh để manual verify)
        """
        logger.info(f"Verify element: {element_description} (timeout: {timeout}s)")

        # Chụp ảnh để verify
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"verify_{element_description.replace(' ', '_')}_{timestamp}.png"

        success = self.take_screenshot(filename, f"Verify: {element_description}")

        if success:
            logger.info(f"Đã chụp ảnh verify: {filename}")
            # Trong tương lai có thể thêm OCR hoặc image recognition ở đây
            # Có thể sử dụng timeout parameter để implement retry logic
            return True
        else:
            logger.error(f"Không thể chụp ảnh verify cho: {element_description}")
            return False

    def wait_for_element(self, element_description: str, max_wait: int = 30,
                        check_interval: int = 2) -> bool:
        """
        Chờ một element xuất hiện trên màn hình

        Args:
            element_description: Mô tả element cần chờ
            max_wait: Thời gian chờ tối đa (giây)
            check_interval: Khoảng thời gian giữa các lần check (giây)

        Returns:
            True nếu element xuất hiện, False nếu timeout
        """
        logger.info(f"Chờ element xuất hiện: {element_description} (tối đa {max_wait}s)")

        start_time = time.time()
        attempt = 1

        while time.time() - start_time < max_wait:
            logger.debug(f"Lần thử {attempt}: Kiểm tra {element_description}")

            # Chụp ảnh để kiểm tra
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"wait_{element_description.replace(' ', '_')}_attempt_{attempt}_{timestamp}.png"

            if self.take_screenshot(filename, f"Wait attempt {attempt}: {element_description}"):
                # Trong tương lai có thể thêm logic phân tích ảnh ở đây
                logger.info(f"Đã chụp ảnh kiểm tra lần {attempt}")

                # Tạm thời return True sau lần thử đầu tiên (có thể customize)
                if attempt >= 1:
                    logger.info(f"Giả định element đã xuất hiện: {element_description}")
                    return True

            attempt += 1
            time.sleep(check_interval)

        logger.warning(f"Timeout chờ element: {element_description}")
        return False

    def run_registration_flow(self) -> bool:
        """
        Chạy quy trình đăng ký TikTok cơ bản với tối ưu cho thiết bị lag

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info("🚀 Bắt đầu quy trình đăng ký TikTok (Tối ưu cho J7 Prime)")

        try:
            # Kiểm tra kết nối device
            if not self.check_device_connection():
                raise TikTokAutomationError("Không thể kết nối với device")

            # Tối ưu hiệu suất thiết bị trước khi bắt đầu
            if self.config.get('settings', {}).get('low_performance_mode', False):
                logger.info("⚡ Tối ưu hiệu suất thiết bị...")
                self.optimize_device_performance()

            # Xóa dữ liệu TikTok
            if not self.clear_tiktok_data():
                raise TikTokAutomationError("Không thể xóa dữ liệu TikTok")

            # Mở TikTok
            if not self.open_tiktok():
                raise TikTokAutomationError("Không thể mở TikTok")

            # Chờ app load với thời gian tăng cho thiết bị lag
            app_load_time = self.delays.get('app_load', 40)
            logger.info(f"⏳ Chờ app load ({app_load_time} giây) - Thiết bị lag cần thời gian...")
            time.sleep(app_load_time)

            # Chụp ảnh màn hình để debug (nếu được bật)
            self.take_screenshot("step1_app_loaded.png", "App đã load xong")

            # Tap "Agree and continue" với retry
            coords = self.coordinates.get('agree_continue', [540, 1800])
            logger.info("👆 Tap 'Agree and continue'")
            if not self.tap_with_retry(coords[0], coords[1], max_retries=3):
                logger.warning("⚠️ Không thể tap 'Agree and continue'")

            # Tap "Skip" với retry
            coords = self.coordinates.get('skip', [200, 1850])
            logger.info("👆 Tap 'Skip'")
            if not self.tap_with_retry(coords[0], coords[1], max_retries=3):
                logger.warning("⚠️ Không thể tap 'Skip'")

            # Chờ sau khi skip với thời gian tăng
            after_skip_time = self.delays.get('after_skip', 15)
            logger.info(f"⏳ Chờ sau khi skip ({after_skip_time} giây)...")
            time.sleep(after_skip_time)

            self.take_screenshot("step2_after_skip.png", "Sau khi skip")

            # Chuyển sang tab Profile
            coords = self.coordinates.get('profile_tab', [950, 1850])
            logger.info("👆 Chuyển sang tab Profile")
            if not self.tap_with_retry(coords[0], coords[1], max_retries=3):
                logger.warning("⚠️ Không thể tap tab Profile")

            time.sleep(self.delays.get('general', 5))
            self.take_screenshot("step3_profile_tab.png", "Tab Profile")

            # Tap Sign up
            coords = self.coordinates.get('signup', [850, 1900])
            logger.info("👆 Tap Sign up")
            if not self.tap_with_retry(coords[0], coords[1], max_retries=3):
                logger.warning("⚠️ Không thể tap Sign up")

            time.sleep(self.delays.get('general', 5))
            self.take_screenshot("step4_signup.png", "Trang Sign up")

            # Tap "Continue with Google"
            coords = self.coordinates.get('continue_with_google', [540, 1300])
            logger.info("👆 Tap 'Continue with Google'")
            if not self.tap_with_retry(coords[0], coords[1], max_retries=3):
                logger.warning("⚠️ Không thể tap 'Continue with Google'")

            time.sleep(self.delays.get('general', 8))  # Chờ lâu hơn cho Google login
            self.take_screenshot("step5_google_login.png", "Google login")

            # Tap vào ô nhập số điện thoại (nếu cần thiết sau Google login)
            coords = self.coordinates.get('phone_input', [540, 1100])
            logger.info("👆 Tap vào ô nhập số điện thoại")
            if not self.tap_with_retry(coords[0], coords[1], max_retries=3):
                logger.warning("⚠️ Không thể tap vào ô nhập số điện thoại")

            self.take_screenshot("step6_phone_input.png", "Ô nhập số điện thoại")

            logger.info("✅ Hoàn thành quy trình đăng ký cơ bản")
            return True

        except TikTokAutomationError as e:
            logger.error(f"❌ Lỗi trong quy trình đăng ký: {e}")
            self.take_screenshot("error_screenshot.png", "Lỗi quy trình")
            return False
        except Exception as e:
            logger.error(f"❌ Lỗi không mong muốn: {e}")
            self.take_screenshot("unexpected_error.png", "Lỗi không mong muốn")
            return False

    def tap_with_retry(self, x: int, y: int, max_retries: int = 3) -> bool:
        """
        Tap với retry cho thiết bị lag

        Args:
            x: Tọa độ x
            y: Tọa độ y
            max_retries: Số lần thử lại tối đa

        Returns:
            True nếu thành công, False nếu thất bại
        """
        for attempt in range(max_retries):
            if attempt > 0:
                logger.info(f"🔄 Thử lại lần {attempt + 1}/{max_retries}")
                time.sleep(2)  # Chờ trước khi thử lại

            if self.tap(x, y):
                return True

        logger.error(f"❌ Tap thất bại sau {max_retries} lần thử tại ({x}, {y})")
        return False

    def handle_google_login(self, email: str = None, password: str = None) -> bool:
        """
        Xử lý Google login trong TikTok

        Args:
            email: Email Google (tùy chọn)
            password: Password Google (tùy chọn)

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info("🔐 Xử lý Google login...")

        try:
            # Chờ Google login screen load
            time.sleep(self.delays.get('general', 8))
            self.take_screenshot("google_login_screen.png", "Google login screen")

            # Nếu có email và password, tự động nhập
            if email and password:
                logger.info(f"📧 Nhập email: {email}")

                # Tap vào ô email (tọa độ có thể cần điều chỉnh)
                email_coords = [540, 800]  # Tọa độ ước tính cho ô email
                if self.tap_with_retry(email_coords[0], email_coords[1]):
                    time.sleep(2)
                    self.input_text(email)

                    # Tap Next
                    next_coords = [540, 1200]
                    self.tap_with_retry(next_coords[0], next_coords[1])
                    time.sleep(3)

                    # Nhập password
                    logger.info("🔑 Nhập password")
                    password_coords = [540, 800]
                    if self.tap_with_retry(password_coords[0], password_coords[1]):
                        time.sleep(2)
                        self.input_text(password)

                        # Tap Next/Sign in
                        signin_coords = [540, 1200]
                        self.tap_with_retry(signin_coords[0], signin_coords[1])

            # Chờ Google login hoàn thành
            logger.info("⏳ Chờ Google login hoàn thành...")
            time.sleep(15)  # Chờ lâu cho Google login

            self.take_screenshot("after_google_login.png", "Sau Google login")

            # Kiểm tra xem có cần chọn tài khoản Google không
            # (Có thể cần thêm logic để handle account selection)

            logger.info("✅ Hoàn thành xử lý Google login")
            return True

        except Exception as e:
            logger.error(f"❌ Lỗi trong Google login: {e}")
            self.take_screenshot("google_login_error.png", "Lỗi Google login")
            return False

    def register_with_google(self, email: str = None, password: str = None) -> bool:
        """
        Đăng ký TikTok bằng Google account

        Args:
            email: Email Google
            password: Password Google

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info("🚀 Bắt đầu đăng ký TikTok bằng Google")

        try:
            # Chạy quy trình đăng ký cơ bản đến bước chọn Google
            if not self.run_registration_flow():
                return False

            # Xử lý Google login
            if not self.handle_google_login(email, password):
                logger.error("❌ Google login thất bại")
                return False

            # Chờ TikTok hoàn thành đăng ký
            logger.info("⏳ Chờ TikTok hoàn thành đăng ký...")
            time.sleep(10)

            self.take_screenshot("registration_completed_google.png", "Đăng ký Google hoàn thành")

            logger.info("✅ Đăng ký TikTok bằng Google thành công!")
            return True

        except Exception as e:
            logger.error(f"❌ Lỗi khi đăng ký bằng Google: {e}")
            self.take_screenshot("google_registration_error.png", "Lỗi đăng ký Google")
            return False

    def register_with_phone(self, phone_number: str) -> bool:
        """
        Đăng ký với số điện thoại

        Args:
            phone_number: Số điện thoại để đăng ký

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info(f"Đăng ký với số điện thoại: {phone_number}")

        try:
            # Chạy quy trình đăng ký cơ bản
            if not self.run_registration_flow():
                return False

            # Xóa input hiện tại (nếu có)
            self.clear_input(10)

            # Nhập số điện thoại
            if not self.input_text(phone_number):
                logger.error("Không thể nhập số điện thoại")
                return False

            self.take_screenshot("phone_entered.png")
            logger.info("Đã nhập số điện thoại thành công")

            # Ở đây có thể thêm logic để tap "Next" hoặc "Send Code"
            # Tùy thuộc vào giao diện của TikTok

            return True

        except Exception as e:
            logger.error(f"Lỗi khi đăng ký với số điện thoại: {e}")
            return False

    def load_accounts(self, filename: str = "accounts.txt") -> list:
        """
        Load danh sách tài khoản từ file

        Args:
            filename: Tên file chứa danh sách tài khoản

        Returns:
            List các tuple (username, password)
        """
        accounts = []
        try:
            if not os.path.exists(filename):
                logger.warning(f"File {filename} không tồn tại")
                return accounts

            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and '|' in line:
                        parts = line.split('|', 1)  # Chỉ split lần đầu tiên
                        if len(parts) == 2:
                            username, password = parts
                            accounts.append((username.strip(), password.strip()))
                        else:
                            logger.warning(f"Dòng {line_num} có format không đúng: {line}")
                    elif line:  # Dòng không rỗng nhưng không có |
                        logger.warning(f"Dòng {line_num} thiếu ký tự '|': {line}")

            logger.info(f"Đã load {len(accounts)} tài khoản từ {filename}")

        except Exception as e:
            logger.error(f"Lỗi khi đọc file {filename}: {e}")

        return accounts

    def register_multiple_accounts(self, accounts_file: str = "accounts.txt",
                                 start_index: int = 0, max_accounts: int = None) -> Dict[str, bool]:
        """
        Đăng ký nhiều tài khoản tự động

        Args:
            accounts_file: File chứa danh sách tài khoản
            start_index: Index bắt đầu trong danh sách tài khoản
            max_accounts: Số lượng tài khoản tối đa cần đăng ký (None = tất cả)

        Returns:
            Dictionary với key là username và value là kết quả đăng ký
        """
        logger.info("Bắt đầu đăng ký nhiều tài khoản")

        # Load danh sách tài khoản
        accounts = self.load_accounts(accounts_file)
        if not accounts:
            logger.error("Không có tài khoản nào để đăng ký")
            return {}

        # Xác định phạm vi tài khoản cần đăng ký
        end_index = len(accounts)
        if max_accounts:
            end_index = min(start_index + max_accounts, len(accounts))

        accounts_to_register = accounts[start_index:end_index]
        logger.info(f"Sẽ đăng ký {len(accounts_to_register)} tài khoản từ index {start_index} đến {end_index-1}")

        results = {}

        for i, (username, password) in enumerate(accounts_to_register, start_index):
            logger.info(f"Đăng ký tài khoản {i+1}/{len(accounts_to_register)}: {username}")

            try:
                # Đăng ký tài khoản
                success = self.register_account(username, password)
                results[username] = success

                if success:
                    logger.info(f"✅ Đăng ký thành công: {username}")
                else:
                    logger.error(f"❌ Đăng ký thất bại: {username}")

                # Chờ giữa các lần đăng ký để tránh bị block
                if i < len(accounts_to_register) - 1:  # Không chờ ở lần cuối
                    delay = self.config.get('settings', {}).get('retry_delay', 5)
                    logger.info(f"Chờ {delay} giây trước khi đăng ký tài khoản tiếp theo...")
                    time.sleep(delay)

            except Exception as e:
                logger.error(f"Lỗi khi đăng ký {username}: {e}")
                results[username] = False

        # Thống kê kết quả
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"Hoàn thành đăng ký: {successful}/{total} tài khoản thành công")

        return results

    def register_account(self, username: str, password: str) -> bool:
        """
        Đăng ký một tài khoản cụ thể

        Args:
            username: Tên đăng nhập
            password: Mật khẩu

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info(f"Đăng ký tài khoản: {username}")

        try:
            # Chạy quy trình đăng ký cơ bản
            if not self.run_registration_flow():
                return False

            # Nhập username (có thể là email hoặc số điện thoại)
            if not self.input_text(username):
                logger.error("Không thể nhập username")
                return False

            self.take_screenshot(f"username_entered_{username}.png")

            # Tap nút "Next" hoặc "Continue"
            next_coords = self.coordinates.get('next_button', [540, 1400])
            if not self.tap(next_coords[0], next_coords[1]):
                logger.warning("Không thể tap nút Next")

            time.sleep(self.delays.get('between_steps', 3))
            self.take_screenshot(f"after_username_{username}.png")

            # Nhập password
            if not self.input_text(password):
                logger.error("Không thể nhập password")
                return False

            self.take_screenshot(f"password_entered_{username}.png")

            # Tap nút "Sign Up" hoặc "Register"
            signup_coords = self.coordinates.get('continue_button', [540, 1600])
            if not self.tap(signup_coords[0], signup_coords[1]):
                logger.warning("Không thể tap nút Sign Up")

            time.sleep(self.delays.get('between_steps', 3))
            self.take_screenshot(f"registration_completed_{username}.png")

            logger.info(f"Hoàn thành quy trình đăng ký cho {username}")
            return True

        except Exception as e:
            logger.error(f"Lỗi khi đăng ký tài khoản {username}: {e}")
            self.take_screenshot(f"error_{username}.png")
            return False


def main():
    """Hàm main để chạy automation"""
    import argparse

    parser = argparse.ArgumentParser(description='TikTok Registration Automation')
    parser.add_argument('--mode', choices=['single', 'multiple', 'basic'], default='basic',
                       help='Chế độ chạy: single (đăng ký 1 tài khoản), multiple (đăng ký nhiều tài khoản), basic (chỉ chạy quy trình cơ bản)')
    parser.add_argument('--username', type=str, help='Username cho chế độ single')
    parser.add_argument('--password', type=str, help='Password cho chế độ single')
    parser.add_argument('--accounts-file', type=str, default='accounts.txt', help='File chứa danh sách tài khoản')
    parser.add_argument('--start-index', type=int, default=0, help='Index bắt đầu trong danh sách tài khoản')
    parser.add_argument('--max-accounts', type=int, help='Số lượng tài khoản tối đa cần đăng ký')
    parser.add_argument('--config', type=str, default='config.json', help='File config')

    args = parser.parse_args()

    try:
        # Khởi tạo automation
        automation = TikTokAutomation(args.config)

        if args.mode == 'basic':
            # Chạy quy trình đăng ký cơ bản
            logger.info("Chạy quy trình đăng ký cơ bản")
            success = automation.run_registration_flow()

            if success:
                logger.info("✅ Quy trình đăng ký cơ bản hoàn thành thành công!")
            else:
                logger.error("❌ Quy trình đăng ký cơ bản thất bại!")

        elif args.mode == 'single':
            # Đăng ký một tài khoản
            if not args.username or not args.password:
                logger.error("Cần cung cấp --username và --password cho chế độ single")
                return

            logger.info(f"Đăng ký tài khoản đơn: {args.username}")
            success = automation.register_account(args.username, args.password)

            if success:
                logger.info(f"✅ Đăng ký thành công tài khoản: {args.username}")
            else:
                logger.error(f"❌ Đăng ký thất bại tài khoản: {args.username}")

        elif args.mode == 'multiple':
            # Đăng ký nhiều tài khoản
            logger.info("Đăng ký nhiều tài khoản")
            results = automation.register_multiple_accounts(
                accounts_file=args.accounts_file,
                start_index=args.start_index,
                max_accounts=args.max_accounts
            )

            successful = sum(1 for success in results.values() if success)
            total = len(results)

            logger.info(f"✅ Kết quả cuối cùng: {successful}/{total} tài khoản đăng ký thành công")

            # In chi tiết kết quả
            for username, success in results.items():
                status = "✅ Thành công" if success else "❌ Thất bại"
                logger.info(f"  {username}: {status}")

    except KeyboardInterrupt:
        logger.info("Người dùng dừng chương trình")
    except Exception as e:
        logger.error(f"Lỗi trong main: {e}")


def create_sample_accounts_file():
    """Tạo file accounts.txt mẫu"""
    sample_accounts = [
        "<EMAIL>|password123",
        "<EMAIL>|password456",
        "**********|mypassword",
        "testuser|testpass"
    ]

    with open("sample_accounts.txt", "w", encoding="utf-8") as f:
        for account in sample_accounts:
            f.write(account + "\n")

    print("Đã tạo file sample_accounts.txt")


if __name__ == "__main__":
    main()
