import subprocess
import time
import logging
import os
import json
from typing import Optional, Dict, Any
from datetime import datetime

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tiktok_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TikTokAutomationError(Exception):
    """Custom exception cho TikTok automation"""
    pass

class TikTokAutomation:
    def __init__(self, config_file: str = "config.json"):
        """
        Khởi tạo TikTok Automation

        Args:
            config_file: Đường dẫn đến file config
        """
        self.config = self._load_config(config_file)
        self.adb_path = self.config.get('adb_path',
            r"C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools\adb.exe")
        self.package_name = self.config.get('package_name', 'com.zhiliaoapp.musically')
        self.coordinates = self.config.get('coordinates', {})
        self.delays = self.config.get('delays', {})

        # Kiểm tra ADB có tồn tại không
        if not os.path.exists(self.adb_path):
            raise TikTokAutomationError(f"ADB không tìm thấy tại: {self.adb_path}")

        logger.info("TikTok Automation đã được khởi tạo")

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load config từ file JSON"""
        default_config = {
            "adb_path": r"C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools\adb.exe",
            "package_name": "com.zhiliaoapp.musically",
            "coordinates": {
                "agree_continue": [540, 1800],
                "skip": [200, 1850],
                "profile_tab": [950, 1850],
                "signup": [850, 1900],
                "phone_input": [540, 1100]
            },
            "delays": {
                "app_load": 40,
                "after_skip": 15,
                "general": 5,
                "tap": 1.5,
                "input": 1.5,
                "clear": 1.0
            }
        }

        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    logger.info(f"Đã load config từ {config_file}")
            else:
                # Tạo file config mặc định
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                logger.info(f"Đã tạo file config mặc định: {config_file}")
        except Exception as e:
            logger.warning(f"Lỗi khi load config: {e}. Sử dụng config mặc định.")

        return default_config

    def _run_adb_command(self, command: list, timeout: int = 30) -> bool:
        """
        Chạy lệnh ADB với error handling

        Args:
            command: List các tham số lệnh ADB
            timeout: Timeout cho lệnh (giây)

        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            full_command = [self.adb_path] + command
            logger.debug(f"Chạy lệnh: {' '.join(full_command)}")

            result = subprocess.run(
                full_command,
                check=True,
                timeout=timeout,
                capture_output=True,
                text=True
            )

            if result.stdout:
                logger.debug(f"Output: {result.stdout.strip()}")

            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Lệnh ADB thất bại: {e}")
            if e.stderr:
                logger.error(f"Error output: {e.stderr}")
            return False
        except subprocess.TimeoutExpired:
            logger.error(f"Lệnh ADB timeout sau {timeout} giây")
            return False
        except Exception as e:
            logger.error(f"Lỗi không mong muốn: {e}")
            return False

    def check_device_connection(self) -> bool:
        """Kiểm tra kết nối với device"""
        logger.info("Kiểm tra kết nối device...")
        return self._run_adb_command(['devices'])

    def clear_tiktok_data(self) -> bool:
        """Xóa dữ liệu TikTok"""
        logger.info("Xóa dữ liệu TikTok...")
        success = self._run_adb_command(['shell', 'pm', 'clear', self.package_name])
        if success:
            time.sleep(self.delays.get('general', 2))
            logger.info("Đã xóa dữ liệu TikTok thành công")
        else:
            logger.error("Không thể xóa dữ liệu TikTok")
        return success

    def open_tiktok(self) -> bool:
        """Mở ứng dụng TikTok"""
        logger.info("Mở ứng dụng TikTok...")
        success = self._run_adb_command([
            'shell', 'monkey', '-p', self.package_name,
            '-c', 'android.intent.category.LAUNCHER', '1'
        ])
        if success:
            time.sleep(self.delays.get('general', 1))
            logger.info("Đã mở TikTok thành công")
        else:
            logger.error("Không thể mở TikTok")
        return success

    def tap(self, x: int, y: int, delay: Optional[float] = None) -> bool:
        """
        Tap tại tọa độ x, y

        Args:
            x: Tọa độ x
            y: Tọa độ y
            delay: Thời gian chờ sau khi tap (giây)
        """
        if delay is None:
            delay = self.delays.get('tap', 1.5)

        logger.info(f"Tap tại ({x}, {y})")
        success = self._run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
        if success:
            time.sleep(delay)
        else:
            logger.error(f"Không thể tap tại ({x}, {y})")
        return success

    def input_text(self, text: str, delay: Optional[float] = None) -> bool:
        """
        Nhập text

        Args:
            text: Text cần nhập
            delay: Thời gian chờ sau khi nhập
        """
        if delay is None:
            delay = self.delays.get('input', 1.5)

        # Xử lý ký tự đặc biệt
        safe_text = text.replace(' ', '%s').replace('&', '\\&')
        logger.info(f"Nhập text: {text}")
        success = self._run_adb_command(['shell', 'input', 'text', safe_text])
        if success:
            time.sleep(delay)
        else:
            logger.error(f"Không thể nhập text: {text}")
        return success

    def clear_input(self, repeat: int = 40, delay: Optional[float] = None) -> bool:
        """
        Xóa input bằng cách nhấn backspace

        Args:
            repeat: Số lần nhấn backspace
            delay: Thời gian chờ sau khi xóa
        """
        if delay is None:
            delay = self.delays.get('clear', 1.0)

        logger.info(f"Xóa input ({repeat} lần)")
        success = True
        for i in range(repeat):
            if not self._run_adb_command(['shell', 'input', 'keyevent', '67']):
                success = False
                logger.error(f"Lỗi khi xóa input lần {i+1}")
                break

        if success:
            time.sleep(delay)
        return success

    def take_screenshot(self, filename: Optional[str] = None, description: str = "") -> bool:
        """
        Chụp ảnh màn hình với tính năng tự động tạo thư mục

        Args:
            filename: Tên file ảnh (nếu None sẽ tự động tạo)
            description: Mô tả cho ảnh chụp
        """
        # Kiểm tra setting có cho phép chụp ảnh không
        if not self.config.get('settings', {}).get('take_screenshots', True):
            return True  # Return True để không làm gián đoạn flow

        # Tạo thư mục screenshots nếu chưa có
        screenshot_dir = self.config.get('settings', {}).get('screenshot_dir', 'screenshots')
        if not os.path.exists(screenshot_dir):
            os.makedirs(screenshot_dir)
            logger.info(f"Đã tạo thư mục: {screenshot_dir}")

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

        # Đảm bảo filename có đường dẫn đầy đủ
        if not os.path.dirname(filename):
            filename = os.path.join(screenshot_dir, filename)

        logger.info(f"Chụp ảnh màn hình: {filename}" + (f" - {description}" if description else ""))

        try:
            # Chụp ảnh trên device
            if not self._run_adb_command(['shell', 'screencap', '/sdcard/screenshot_temp.png']):
                return False

            # Pull ảnh về máy tính
            success = self._run_adb_command(['pull', '/sdcard/screenshot_temp.png', filename])
            if success:
                logger.info(f"Đã lưu ảnh: {filename}")
                # Xóa file tạm trên device
                self._run_adb_command(['shell', 'rm', '/sdcard/screenshot_temp.png'])
            else:
                logger.error("Không thể lưu ảnh màn hình")

            return success

        except Exception as e:
            logger.error(f"Lỗi khi chụp ảnh màn hình: {e}")
            return False

    def verify_element_exists(self, element_description: str, timeout: int = 10) -> bool:
        """
        Verify xem một element có tồn tại trên màn hình không bằng cách chụp ảnh

        Args:
            element_description: Mô tả element cần tìm
            timeout: Thời gian chờ tối đa (giây)

        Returns:
            True nếu element được tìm thấy (hiện tại chỉ chụp ảnh để manual verify)
        """
        logger.info(f"Verify element: {element_description}")

        # Chụp ảnh để verify
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"verify_{element_description.replace(' ', '_')}_{timestamp}.png"

        success = self.take_screenshot(filename, f"Verify: {element_description}")

        if success:
            logger.info(f"Đã chụp ảnh verify: {filename}")
            # Trong tương lai có thể thêm OCR hoặc image recognition ở đây
            return True
        else:
            logger.error(f"Không thể chụp ảnh verify cho: {element_description}")
            return False

    def wait_for_element(self, element_description: str, max_wait: int = 30,
                        check_interval: int = 2) -> bool:
        """
        Chờ một element xuất hiện trên màn hình

        Args:
            element_description: Mô tả element cần chờ
            max_wait: Thời gian chờ tối đa (giây)
            check_interval: Khoảng thời gian giữa các lần check (giây)

        Returns:
            True nếu element xuất hiện, False nếu timeout
        """
        logger.info(f"Chờ element xuất hiện: {element_description} (tối đa {max_wait}s)")

        start_time = time.time()
        attempt = 1

        while time.time() - start_time < max_wait:
            logger.debug(f"Lần thử {attempt}: Kiểm tra {element_description}")

            # Chụp ảnh để kiểm tra
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"wait_{element_description.replace(' ', '_')}_attempt_{attempt}_{timestamp}.png"

            if self.take_screenshot(filename, f"Wait attempt {attempt}: {element_description}"):
                # Trong tương lai có thể thêm logic phân tích ảnh ở đây
                logger.info(f"Đã chụp ảnh kiểm tra lần {attempt}")

                # Tạm thời return True sau lần thử đầu tiên (có thể customize)
                if attempt >= 1:
                    logger.info(f"Giả định element đã xuất hiện: {element_description}")
                    return True

            attempt += 1
            time.sleep(check_interval)

        logger.warning(f"Timeout chờ element: {element_description}")
        return False

    def run_registration_flow(self) -> bool:
        """
        Chạy quy trình đăng ký TikTok cơ bản

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info("Bắt đầu quy trình đăng ký TikTok")

        try:
            # Kiểm tra kết nối device
            if not self.check_device_connection():
                raise TikTokAutomationError("Không thể kết nối với device")

            # Xóa dữ liệu TikTok
            if not self.clear_tiktok_data():
                raise TikTokAutomationError("Không thể xóa dữ liệu TikTok")

            # Mở TikTok
            if not self.open_tiktok():
                raise TikTokAutomationError("Không thể mở TikTok")

            # Chờ app load
            logger.info(f"Chờ app load ({self.delays.get('app_load', 40)} giây)...")
            time.sleep(self.delays.get('app_load', 40))

            # Chụp ảnh màn hình để debug
            self.take_screenshot("step1_app_loaded.png")

            # Tap "Agree and continue"
            coords = self.coordinates.get('agree_continue', [540, 1800])
            if not self.tap(coords[0], coords[1]):
                logger.warning("Không thể tap 'Agree and continue'")

            # Tap "Skip"
            coords = self.coordinates.get('skip', [200, 1850])
            if not self.tap(coords[0], coords[1]):
                logger.warning("Không thể tap 'Skip'")

            # Chờ sau khi skip
            logger.info(f"Chờ sau khi skip ({self.delays.get('after_skip', 15)} giây)...")
            time.sleep(self.delays.get('after_skip', 15))

            self.take_screenshot("step2_after_skip.png")

            # Chuyển sang tab Profile
            coords = self.coordinates.get('profile_tab', [950, 1850])
            if not self.tap(coords[0], coords[1]):
                logger.warning("Không thể tap tab Profile")

            time.sleep(self.delays.get('general', 5))
            self.take_screenshot("step3_profile_tab.png")

            # Tap Sign up
            coords = self.coordinates.get('signup', [850, 1900])
            if not self.tap(coords[0], coords[1]):
                logger.warning("Không thể tap Sign up")

            time.sleep(self.delays.get('general', 5))
            self.take_screenshot("step4_signup.png")

            # Tap vào ô nhập số điện thoại
            coords = self.coordinates.get('phone_input', [540, 1100])
            if not self.tap(coords[0], coords[1]):
                logger.warning("Không thể tap vào ô nhập số điện thoại")

            self.take_screenshot("step5_phone_input.png")

            logger.info("Hoàn thành quy trình đăng ký cơ bản")
            return True

        except TikTokAutomationError as e:
            logger.error(f"Lỗi trong quy trình đăng ký: {e}")
            self.take_screenshot("error_screenshot.png")
            return False
        except Exception as e:
            logger.error(f"Lỗi không mong muốn: {e}")
            self.take_screenshot("unexpected_error.png")
            return False

    def register_with_phone(self, phone_number: str) -> bool:
        """
        Đăng ký với số điện thoại

        Args:
            phone_number: Số điện thoại để đăng ký

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info(f"Đăng ký với số điện thoại: {phone_number}")

        try:
            # Chạy quy trình đăng ký cơ bản
            if not self.run_registration_flow():
                return False

            # Xóa input hiện tại (nếu có)
            self.clear_input(10)

            # Nhập số điện thoại
            if not self.input_text(phone_number):
                logger.error("Không thể nhập số điện thoại")
                return False

            self.take_screenshot("phone_entered.png")
            logger.info("Đã nhập số điện thoại thành công")

            # Ở đây có thể thêm logic để tap "Next" hoặc "Send Code"
            # Tùy thuộc vào giao diện của TikTok

            return True

        except Exception as e:
            logger.error(f"Lỗi khi đăng ký với số điện thoại: {e}")
            return False

    def load_accounts(self, filename: str = "accounts.txt") -> list:
        """
        Load danh sách tài khoản từ file

        Args:
            filename: Tên file chứa danh sách tài khoản

        Returns:
            List các tuple (username, password)
        """
        accounts = []
        try:
            if not os.path.exists(filename):
                logger.warning(f"File {filename} không tồn tại")
                return accounts

            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and '|' in line:
                        parts = line.split('|', 1)  # Chỉ split lần đầu tiên
                        if len(parts) == 2:
                            username, password = parts
                            accounts.append((username.strip(), password.strip()))
                        else:
                            logger.warning(f"Dòng {line_num} có format không đúng: {line}")
                    elif line:  # Dòng không rỗng nhưng không có |
                        logger.warning(f"Dòng {line_num} thiếu ký tự '|': {line}")

            logger.info(f"Đã load {len(accounts)} tài khoản từ {filename}")

        except Exception as e:
            logger.error(f"Lỗi khi đọc file {filename}: {e}")

        return accounts

    def register_multiple_accounts(self, accounts_file: str = "accounts.txt",
                                 start_index: int = 0, max_accounts: int = None) -> Dict[str, bool]:
        """
        Đăng ký nhiều tài khoản tự động

        Args:
            accounts_file: File chứa danh sách tài khoản
            start_index: Index bắt đầu trong danh sách tài khoản
            max_accounts: Số lượng tài khoản tối đa cần đăng ký (None = tất cả)

        Returns:
            Dictionary với key là username và value là kết quả đăng ký
        """
        logger.info("Bắt đầu đăng ký nhiều tài khoản")

        # Load danh sách tài khoản
        accounts = self.load_accounts(accounts_file)
        if not accounts:
            logger.error("Không có tài khoản nào để đăng ký")
            return {}

        # Xác định phạm vi tài khoản cần đăng ký
        end_index = len(accounts)
        if max_accounts:
            end_index = min(start_index + max_accounts, len(accounts))

        accounts_to_register = accounts[start_index:end_index]
        logger.info(f"Sẽ đăng ký {len(accounts_to_register)} tài khoản từ index {start_index} đến {end_index-1}")

        results = {}

        for i, (username, password) in enumerate(accounts_to_register, start_index):
            logger.info(f"Đăng ký tài khoản {i+1}/{len(accounts_to_register)}: {username}")

            try:
                # Đăng ký tài khoản
                success = self.register_account(username, password)
                results[username] = success

                if success:
                    logger.info(f"✅ Đăng ký thành công: {username}")
                else:
                    logger.error(f"❌ Đăng ký thất bại: {username}")

                # Chờ giữa các lần đăng ký để tránh bị block
                if i < len(accounts_to_register) - 1:  # Không chờ ở lần cuối
                    delay = self.config.get('settings', {}).get('retry_delay', 5)
                    logger.info(f"Chờ {delay} giây trước khi đăng ký tài khoản tiếp theo...")
                    time.sleep(delay)

            except Exception as e:
                logger.error(f"Lỗi khi đăng ký {username}: {e}")
                results[username] = False

        # Thống kê kết quả
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"Hoàn thành đăng ký: {successful}/{total} tài khoản thành công")

        return results

    def register_account(self, username: str, password: str) -> bool:
        """
        Đăng ký một tài khoản cụ thể

        Args:
            username: Tên đăng nhập
            password: Mật khẩu

        Returns:
            True nếu thành công, False nếu thất bại
        """
        logger.info(f"Đăng ký tài khoản: {username}")

        try:
            # Chạy quy trình đăng ký cơ bản
            if not self.run_registration_flow():
                return False

            # Nhập username (có thể là email hoặc số điện thoại)
            if not self.input_text(username):
                logger.error("Không thể nhập username")
                return False

            self.take_screenshot(f"username_entered_{username}.png")

            # Tap nút "Next" hoặc "Continue"
            next_coords = self.coordinates.get('next_button', [540, 1400])
            if not self.tap(next_coords[0], next_coords[1]):
                logger.warning("Không thể tap nút Next")

            time.sleep(self.delays.get('between_steps', 3))
            self.take_screenshot(f"after_username_{username}.png")

            # Nhập password
            if not self.input_text(password):
                logger.error("Không thể nhập password")
                return False

            self.take_screenshot(f"password_entered_{username}.png")

            # Tap nút "Sign Up" hoặc "Register"
            signup_coords = self.coordinates.get('continue_button', [540, 1600])
            if not self.tap(signup_coords[0], signup_coords[1]):
                logger.warning("Không thể tap nút Sign Up")

            time.sleep(self.delays.get('between_steps', 3))
            self.take_screenshot(f"registration_completed_{username}.png")

            logger.info(f"Hoàn thành quy trình đăng ký cho {username}")
            return True

        except Exception as e:
            logger.error(f"Lỗi khi đăng ký tài khoản {username}: {e}")
            self.take_screenshot(f"error_{username}.png")
            return False


def main():
    """Hàm main để chạy automation"""
    import argparse

    parser = argparse.ArgumentParser(description='TikTok Registration Automation')
    parser.add_argument('--mode', choices=['single', 'multiple', 'basic'], default='basic',
                       help='Chế độ chạy: single (đăng ký 1 tài khoản), multiple (đăng ký nhiều tài khoản), basic (chỉ chạy quy trình cơ bản)')
    parser.add_argument('--username', type=str, help='Username cho chế độ single')
    parser.add_argument('--password', type=str, help='Password cho chế độ single')
    parser.add_argument('--accounts-file', type=str, default='accounts.txt', help='File chứa danh sách tài khoản')
    parser.add_argument('--start-index', type=int, default=0, help='Index bắt đầu trong danh sách tài khoản')
    parser.add_argument('--max-accounts', type=int, help='Số lượng tài khoản tối đa cần đăng ký')
    parser.add_argument('--config', type=str, default='config.json', help='File config')

    args = parser.parse_args()

    try:
        # Khởi tạo automation
        automation = TikTokAutomation(args.config)

        if args.mode == 'basic':
            # Chạy quy trình đăng ký cơ bản
            logger.info("Chạy quy trình đăng ký cơ bản")
            success = automation.run_registration_flow()

            if success:
                logger.info("✅ Quy trình đăng ký cơ bản hoàn thành thành công!")
            else:
                logger.error("❌ Quy trình đăng ký cơ bản thất bại!")

        elif args.mode == 'single':
            # Đăng ký một tài khoản
            if not args.username or not args.password:
                logger.error("Cần cung cấp --username và --password cho chế độ single")
                return

            logger.info(f"Đăng ký tài khoản đơn: {args.username}")
            success = automation.register_account(args.username, args.password)

            if success:
                logger.info(f"✅ Đăng ký thành công tài khoản: {args.username}")
            else:
                logger.error(f"❌ Đăng ký thất bại tài khoản: {args.username}")

        elif args.mode == 'multiple':
            # Đăng ký nhiều tài khoản
            logger.info("Đăng ký nhiều tài khoản")
            results = automation.register_multiple_accounts(
                accounts_file=args.accounts_file,
                start_index=args.start_index,
                max_accounts=args.max_accounts
            )

            successful = sum(1 for success in results.values() if success)
            total = len(results)

            logger.info(f"✅ Kết quả cuối cùng: {successful}/{total} tài khoản đăng ký thành công")

            # In chi tiết kết quả
            for username, success in results.items():
                status = "✅ Thành công" if success else "❌ Thất bại"
                logger.info(f"  {username}: {status}")

    except KeyboardInterrupt:
        logger.info("Người dùng dừng chương trình")
    except Exception as e:
        logger.error(f"Lỗi trong main: {e}")


def create_sample_accounts_file():
    """Tạo file accounts.txt mẫu"""
    sample_accounts = [
        "<EMAIL>|password123",
        "<EMAIL>|password456",
        "**********|mypassword",
        "testuser|testpass"
    ]

    with open("sample_accounts.txt", "w", encoding="utf-8") as f:
        for account in sample_accounts:
            f.write(account + "\n")

    print("Đã tạo file sample_accounts.txt")


if __name__ == "__main__":
    main()
