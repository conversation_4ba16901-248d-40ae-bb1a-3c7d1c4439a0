#!/usr/bin/env python3
"""
TikTok Registration Automation - Phiên bản đơn giản cho J7 Prime
Tất cả trong 1 file duy nhất!
"""

import subprocess
import time
import logging
import os
import json
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class TikTokSimple:
    def __init__(self):
        # <PERSON><PERSON><PERSON> hình cơ bản cho J7 Prime
        self.adb_path = r"C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools\adb.exe"
        self.package = "com.zhiliaoapp.musically"
        
        # Tọa độ cho J7 Prime (có thể cần điều chỉnh)
        self.coords = {
            'agree': [540, 1800],
            'skip': [200, 1850], 
            'profile': [950, 1850],
            'signup': [850, 1900],
            'google': [540, 1300],
            'phone': [540, 1100]
        }
        
        # Th<PERSON>i gian chờ cho J7 Prime lag
        self.delays = {
            'app_load': 80,
            'after_skip': 35,
            'tap': 4.0,
            'general': 8
        }
    
    def run_adb(self, cmd):
        """Chạy lệnh ADB"""
        try:
            full_cmd = [self.adb_path] + cmd
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=60)
            return result.returncode == 0
        except:
            return False
    
    def tap(self, x, y):
        """Tap tại tọa độ x,y"""
        logger.info(f"👆 Tap ({x}, {y})")
        success = self.run_adb(['shell', 'input', 'tap', str(x), str(y)])
        time.sleep(self.delays['tap'])
        return success
    
    def input_text(self, text):
        """Nhập text"""
        logger.info(f"⌨️ Nhập: {text}")
        safe_text = text.replace(' ', '%s')
        success = self.run_adb(['shell', 'input', 'text', safe_text])
        time.sleep(self.delays['tap'])
        return success
    
    def optimize_device(self):
        """Tối ưu J7 Prime"""
        logger.info("⚡ Tối ưu thiết bị...")
        commands = [
            ['shell', 'settings', 'put', 'global', 'window_animation_scale', '0'],
            ['shell', 'settings', 'put', 'global', 'transition_animation_scale', '0'],
            ['shell', 'am', 'kill-all']
        ]
        for cmd in commands:
            self.run_adb(cmd)
            time.sleep(1)
    
    def clear_tiktok(self):
        """Xóa dữ liệu TikTok"""
        logger.info("🗑️ Xóa dữ liệu TikTok...")
        return self.run_adb(['shell', 'pm', 'clear', self.package])
    
    def open_tiktok(self):
        """Mở TikTok"""
        logger.info("📱 Mở TikTok...")
        return self.run_adb(['shell', 'monkey', '-p', self.package, '-c', 'android.intent.category.LAUNCHER', '1'])
    
    def run_basic_flow(self):
        """Chạy quy trình cơ bản đến Google login"""
        logger.info("🚀 Bắt đầu automation...")
        
        # Kiểm tra kết nối
        if not self.run_adb(['devices']):
            logger.error("❌ Không kết nối được thiết bị")
            return False
        
        # Tối ưu thiết bị
        self.optimize_device()
        
        # Xóa dữ liệu và mở app
        if not self.clear_tiktok():
            logger.error("❌ Không thể xóa dữ liệu")
            return False
        
        time.sleep(3)
        
        if not self.open_tiktok():
            logger.error("❌ Không thể mở TikTok")
            return False
        
        # Chờ app load
        logger.info(f"⏳ Chờ app load ({self.delays['app_load']}s)...")
        time.sleep(self.delays['app_load'])
        
        # Các bước tap
        steps = [
            ("Agree and continue", self.coords['agree']),
            ("Skip", self.coords['skip']),
            ("Profile tab", self.coords['profile']),
            ("Sign up", self.coords['signup']),
            ("Continue with Google", self.coords['google'])
        ]
        
        for step_name, (x, y) in steps:
            logger.info(f"📍 Bước: {step_name}")
            self.tap(x, y)
            
            if step_name == "Skip":
                logger.info(f"⏳ Chờ sau skip ({self.delays['after_skip']}s)...")
                time.sleep(self.delays['after_skip'])
            else:
                time.sleep(self.delays['general'])
        
        logger.info("✅ Hoàn thành! Bây giờ bạn có thể đăng nhập Google thủ công")
        return True
    
    def register_with_account(self, username, password):
        """Đăng ký với username/password"""
        logger.info(f"🎯 Đăng ký tài khoản: {username}")
        
        if not self.run_basic_flow():
            return False
        
        # Tap vào ô phone và nhập thông tin
        time.sleep(5)
        self.tap(self.coords['phone'][0], self.coords['phone'][1])
        time.sleep(2)
        
        self.input_text(username)
        time.sleep(3)
        
        # Tap Next (tọa độ ước tính)
        self.tap(540, 1400)
        time.sleep(3)
        
        self.input_text(password)
        time.sleep(3)
        
        # Tap Sign up
        self.tap(540, 1600)
        
        logger.info("✅ Hoàn thành đăng ký!")
        return True

def print_banner():
    print("=" * 50)
    print("🔧 TikTok Automation - J7 Prime (Đơn giản)")
    print("=" * 50)
    print("📱 Thiết bị: Samsung Galaxy J7 Prime")
    print("⚡ Chế độ: Tối ưu cho lag")
    print("=" * 50)

def main():
    print_banner()
    
    # Kiểm tra chuẩn bị
    print("\n✅ Checklist:")
    print("- USB Debugging đã bật")
    print("- Kết nối USB ổn định") 
    print("- TikTok đã cài đặt")
    print("- Đóng tất cả app khác")
    print("- Sạc pin > 50%")
    
    input("\n📱 Nhấn Enter khi đã sẵn sàng...")
    
    automation = TikTokSimple()
    
    print("\n📋 Chọn chế độ:")
    print("1. Quy trình cơ bản (đến Google login)")
    print("2. Đăng ký với username/password")
    print("3. Thoát")
    
    choice = input("\nNhập lựa chọn (1-3): ").strip()
    
    if choice == "1":
        automation.run_basic_flow()
        
    elif choice == "2":
        username = input("Nhập username/email/phone: ").strip()
        password = input("Nhập password: ").strip()
        
        if username and password:
            automation.register_with_account(username, password)
        else:
            print("❌ Vui lòng nhập đầy đủ thông tin")
            
    elif choice == "3":
        print("👋 Tạm biệt!")
        
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
