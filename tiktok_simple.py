#!/usr/bin/env python3
"""
TikTok Registration Automation - Phiên bản đơn giản cho J7 Prime
Tất cả trong 1 file duy nhất!
"""

import subprocess
import time
import logging
import os
import json
import re
import imaplib
import email
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class TikTokSimple:
    def __init__(self):
        # <PERSON><PERSON><PERSON> hình cơ bản cho J7 Prime
        self.adb_path = r"C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools\adb.exe"
        self.package = "com.zhiliaoapp.musically"
        
        # Tọa độ cho J7 Prime (có thể cần điều chỉnh)
        self.coords = {
            'agree': [540, 1800],
            'skip': [200, 1850],
            'profile': [950, 1850],
            'signup': [850, 1900],
            'google': [540, 1200],  # Th<PERSON> tọa độ kh<PERSON><PERSON> cho nút Google
            'google2': [540, 1300], # Tọa độ dự phòng
            'google3': [540, 1400], # Tọa độ dự phòng 2
            'phone': [540, 1100]
        }
        
        # Thời gian chờ cho J7 Prime lag
        self.delays = {
            'app_load': 80,
            'after_skip': 35,
            'tap': 4.0,
            'general': 8
        }
    
    def run_adb(self, cmd):
        """Chạy lệnh ADB"""
        try:
            full_cmd = [self.adb_path] + cmd
            result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=60)
            return result.returncode == 0
        except:
            return False
    
    def tap(self, x, y):
        """Tap tại tọa độ x,y"""
        logger.info(f"👆 Tap ({x}, {y})")
        success = self.run_adb(['shell', 'input', 'tap', str(x), str(y)])
        time.sleep(self.delays['tap'])
        return success
    
    def input_text(self, text):
        """Nhập text"""
        logger.info(f"⌨️ Nhập: {text}")
        safe_text = text.replace(' ', '%s')
        success = self.run_adb(['shell', 'input', 'text', safe_text])
        time.sleep(self.delays['tap'])
        return success
    
    def optimize_device(self):
        """Tối ưu J7 Prime"""
        logger.info("⚡ Tối ưu thiết bị...")
        commands = [
            ['shell', 'settings', 'put', 'global', 'window_animation_scale', '0'],
            ['shell', 'settings', 'put', 'global', 'transition_animation_scale', '0'],
            ['shell', 'am', 'kill-all']
        ]
        for cmd in commands:
            self.run_adb(cmd)
            time.sleep(1)
    
    def clear_tiktok(self):
        """Xóa dữ liệu TikTok"""
        logger.info("🗑️ Xóa dữ liệu TikTok...")
        return self.run_adb(['shell', 'pm', 'clear', self.package])
    
    def open_tiktok(self):
        """Mở TikTok"""
        logger.info("📱 Mở TikTok...")
        return self.run_adb(['shell', 'monkey', '-p', self.package, '-c', 'android.intent.category.LAUNCHER', '1'])
    
    def take_screenshot(self, filename):
        """Chụp ảnh màn hình để debug"""
        try:
            self.run_adb(['shell', 'screencap', '/sdcard/temp.png'])
            self.run_adb(['pull', '/sdcard/temp.png', filename])
            logger.info(f"📸 Đã chụp ảnh: {filename}")
            return True
        except:
            return False

    def get_screen_text(self):
        """Lấy text trên màn hình bằng uiautomator"""
        try:
            result = subprocess.run([self.adb_path, 'shell', 'uiautomator', 'dump', '/sdcard/ui.xml'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # Pull file XML về
                subprocess.run([self.adb_path, 'pull', '/sdcard/ui.xml', 'ui_dump.xml'],
                             capture_output=True, timeout=10)

                # Đọc và tìm text
                try:
                    with open('ui_dump.xml', 'r', encoding='utf-8') as f:
                        content = f.read().lower()
                        return content
                except:
                    return ""
            return ""
        except:
            return ""

    def wait_for_element(self, text_to_find, max_wait=30, check_interval=2):
        """Chờ thông minh - tìm thấy element thì return ngay"""
        logger.info(f"🔍 Chờ xuất hiện: '{text_to_find}' (tối đa {max_wait}s)")

        start_time = time.time()
        attempt = 1

        while time.time() - start_time < max_wait:
            logger.info(f"⏳ Lần kiểm tra {attempt}...")

            # Lấy text trên màn hình
            screen_content = self.get_screen_text()

            if text_to_find.lower() in screen_content:
                logger.info(f"✅ Tìm thấy '{text_to_find}' sau {time.time() - start_time:.1f}s!")
                return True

            # Chụp ảnh để debug
            self.take_screenshot(f"wait_attempt_{attempt}.png")

            attempt += 1
            time.sleep(check_interval)

        logger.warning(f"⏰ Timeout chờ '{text_to_find}' sau {max_wait}s")
        return False

    def smart_tap_when_ready(self, coords_list, text_to_wait, max_wait=20):
        """Tap thông minh - chờ text xuất hiện rồi mới tap"""
        if self.wait_for_element(text_to_wait, max_wait):
            # Thử từng tọa độ cho đến khi thành công
            for i, (x, y) in enumerate(coords_list, 1):
                logger.info(f"👆 Thử tap vị trí {i}: ({x}, {y})")
                self.tap(x, y)

                # Chờ 3s xem có thay đổi gì không
                time.sleep(3)
                new_content = self.get_screen_text()

                # Nếu text biến mất = tap thành công
                if text_to_wait.lower() not in new_content:
                    logger.info(f"✅ Tap thành công tại vị trí {i}!")
                    return True

            logger.warning("⚠️ Đã thử tất cả vị trí nhưng không thành công")
            return False
        else:
            logger.error(f"❌ Không tìm thấy '{text_to_wait}' để tap")
            return False

    def find_google_button(self):
        """Thử tìm nút Continue with Google ở nhiều vị trí"""
        logger.info("🔍 Tìm nút 'Continue with Google'...")

        # Chụp ảnh để debug
        self.take_screenshot("debug_signup_screen.png")

        # Thử nhiều tọa độ khác nhau
        google_positions = [
            [540, 1200],  # Vị trí 1
            [540, 1300],  # Vị trí 2
            [540, 1400],  # Vị trí 3
            [540, 1500],  # Vị trí 4
            [540, 1000],  # Vị trí 5 (cao hơn)
            [540, 900],   # Vị trí 6 (cao hơn nữa)
        ]

        for i, (x, y) in enumerate(google_positions, 1):
            logger.info(f"🎯 Thử vị trí {i}: ({x}, {y})")
            self.tap(x, y)
            time.sleep(3)

            # Chụp ảnh sau mỗi lần tap để xem có gì thay đổi không
            self.take_screenshot(f"after_tap_{i}.png")

            # Kiểm tra xem có chuyển sang màn hình Google login không
            # (Tạm thời chờ và chụp ảnh)
            time.sleep(2)

        logger.info("🔍 Đã thử tất cả vị trí. Kiểm tra ảnh để xem kết quả!")
        return True

    def run_smart_flow(self):
        """Chạy quy trình THÔNG MINH - chờ element xuất hiện rồi mới click"""
        logger.info("🚀 Bắt đầu automation THÔNG MINH...")

        # Kiểm tra kết nối
        if not self.run_adb(['devices']):
            logger.error("❌ Không kết nối được thiết bị")
            return False

        # Tối ưu thiết bị
        self.optimize_device()

        # Xóa dữ liệu và mở app
        if not self.clear_tiktok():
            logger.error("❌ Không thể xóa dữ liệu")
            return False

        time.sleep(3)

        if not self.open_tiktok():
            logger.error("❌ Không thể mở TikTok")
            return False

        # Bước 1: Chờ và tap "Agree"
        logger.info("📍 Bước 1: Chờ nút 'Agree'...")
        agree_coords = [[540, 1800], [540, 1850], [540, 1750]]
        if not self.smart_tap_when_ready(agree_coords, "agree", 60):
            logger.error("❌ Không tìm thấy nút Agree")
            return False

        # Bước 2: Chờ và tap "Skip"
        logger.info("📍 Bước 2: Chờ nút 'Skip'...")
        skip_coords = [[200, 1850], [150, 1850], [250, 1850]]
        if not self.smart_tap_when_ready(skip_coords, "skip", 30):
            logger.warning("⚠️ Không tìm thấy nút Skip, tiếp tục...")

        # Bước 3: Chờ và chuyển sang Profile
        logger.info("📍 Bước 3: Chờ tab 'Profile'...")
        profile_coords = [[950, 1850], [900, 1850], [1000, 1850]]
        if not self.smart_tap_when_ready(profile_coords, "profile", 30):
            logger.warning("⚠️ Không tìm thấy tab Profile, thử tìm Me...")
            if not self.smart_tap_when_ready(profile_coords, "me", 20):
                logger.error("❌ Không tìm thấy tab Profile/Me")
                return False

        # Bước 4: Chờ và tap "Sign up"
        logger.info("📍 Bước 4: Chờ nút 'Sign up'...")
        signup_coords = [[850, 1900], [800, 1900], [900, 1900], [540, 1900]]
        if not self.smart_tap_when_ready(signup_coords, "sign up", 30):
            logger.error("❌ Không tìm thấy nút Sign up")
            return False

        # Bước 5: Chờ và tap "Continue with Google"
        logger.info("📍 Bước 5: Chờ nút 'Continue with Google'...")
        google_coords = [
            [540, 1200], [540, 1300], [540, 1400], [540, 1500],
            [540, 1000], [540, 900], [540, 1100], [540, 1600]
        ]
        if self.smart_tap_when_ready(google_coords, "google", 30):
            logger.info("🎉 Đã click vào Continue with Google!")

            # Chờ Google login screen
            if self.wait_for_element("google", 15):
                logger.info("✅ Đã chuyển sang Google login!")
            else:
                logger.info("✅ Đã click Google, bạn có thể đăng nhập thủ công")
        else:
            logger.warning("⚠️ Không tìm thấy nút Continue with Google")
            logger.info("💡 Có thể bạn cần đăng ký bằng cách khác")

        logger.info("✅ Hoàn thành automation thông minh!")
        return True

    def read_sms_code(self):
        """Đọc mã xác thực từ SMS"""
        logger.info("📱 Đọc mã SMS...")
        try:
            # Đọc SMS gần nhất
            result = subprocess.run([
                self.adb_path, 'shell',
                'content', 'query', '--uri', 'content://sms/inbox',
                '--projection', 'body,date',
                '--sort', 'date DESC',
                '--limit', '5'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                sms_content = result.stdout
                logger.info(f"📱 SMS content: {sms_content}")

                # Tìm mã 6 số hoặc 4 số
                codes = re.findall(r'\b\d{4,6}\b', sms_content)
                if codes:
                    latest_code = codes[0]
                    logger.info(f"🔢 Tìm thấy mã: {latest_code}")
                    return latest_code

            return None
        except Exception as e:
            logger.error(f"❌ Lỗi đọc SMS: {e}")
            return None

    def read_gmail_code(self, email_address, password, max_wait=60):
        """Đọc mã xác thực từ Gmail"""
        logger.info(f"📧 Đọc mã từ Gmail: {email_address}")

        try:
            # Kết nối Gmail IMAP
            mail = imaplib.IMAP4_SSL('imap.gmail.com')
            mail.login(email_address, password)
            mail.select('inbox')

            start_time = time.time()

            while time.time() - start_time < max_wait:
                logger.info("🔍 Tìm email mới...")

                # Tìm email từ TikTok trong 5 phút gần nhất
                search_criteria = '(FROM "tiktok" OR FROM "bytedance" OR SUBJECT "verification" OR SUBJECT "code") SINCE "' + \
                                datetime.now().strftime('%d-%b-%Y') + '"'

                status, messages = mail.search(None, search_criteria)

                if status == 'OK' and messages[0]:
                    # Lấy email mới nhất
                    latest_email_id = messages[0].split()[-1]

                    status, msg_data = mail.fetch(latest_email_id, '(RFC822)')

                    if status == 'OK':
                        email_body = msg_data[0][1].decode('utf-8')

                        # Tìm mã xác thực trong email
                        codes = re.findall(r'\b\d{4,6}\b', email_body)
                        if codes:
                            verification_code = codes[0]
                            logger.info(f"🔢 Tìm thấy mã Gmail: {verification_code}")
                            mail.logout()
                            return verification_code

                time.sleep(5)  # Chờ 5s rồi check lại

            mail.logout()
            logger.warning("⏰ Timeout chờ email verification")
            return None

        except Exception as e:
            logger.error(f"❌ Lỗi đọc Gmail: {e}")
            return None

    def auto_handle_verification(self, email_address=None, email_password=None):
        """Tự động xử lý verification code"""
        logger.info("🔐 Tự động xử lý verification...")

        # Chờ màn hình verification xuất hiện
        if self.wait_for_element("verification", 30):
            logger.info("📱 Đã vào màn hình verification")

            # Thử đọc SMS trước
            sms_code = self.read_sms_code()

            if sms_code:
                logger.info(f"📱 Sử dụng mã SMS: {sms_code}")
                return self.input_verification_code(sms_code)

            # Nếu không có SMS, thử Gmail
            elif email_address and email_password:
                logger.info("📧 Thử đọc mã từ Gmail...")
                gmail_code = self.read_gmail_code(email_address, email_password)

                if gmail_code:
                    logger.info(f"📧 Sử dụng mã Gmail: {gmail_code}")
                    return self.input_verification_code(gmail_code)

            # Nếu không đọc được, yêu cầu nhập thủ công
            logger.info("⌨️ Không đọc được mã tự động, nhập thủ công:")
            manual_code = input("Nhập mã verification: ").strip()

            if manual_code:
                return self.input_verification_code(manual_code)

        return False

    def input_verification_code(self, code):
        """Nhập mã verification vào TikTok"""
        logger.info(f"⌨️ Nhập mã verification: {code}")

        # Tọa độ ô nhập mã (có thể cần điều chỉnh)
        code_input_coords = [
            [540, 1200], [540, 1100], [540, 1300], [540, 1000]
        ]

        # Thử tap vào ô nhập mã
        for x, y in code_input_coords:
            self.tap(x, y)
            time.sleep(1)

            # Nhập mã
            if self.input_text(code):
                time.sleep(2)

                # Tap nút Submit/Continue
                submit_coords = [[540, 1400], [540, 1500], [540, 1600]]
                for sx, sy in submit_coords:
                    self.tap(sx, sy)
                    time.sleep(3)

                    # Kiểm tra xem có chuyển màn hình không
                    if not self.wait_for_element("verification", 5):
                        logger.info("✅ Verification thành công!")
                        return True

        logger.error("❌ Không thể nhập mã verification")
        return False

    def run_basic_flow(self):
        """Chạy quy trình cơ bản (cũ) với delay cứng"""
        logger.info("🚀 Bắt đầu automation cơ bản...")

        # Kiểm tra kết nối
        if not self.run_adb(['devices']):
            logger.error("❌ Không kết nối được thiết bị")
            return False

        # Tối ưu thiết bị
        self.optimize_device()

        # Xóa dữ liệu và mở app
        if not self.clear_tiktok():
            logger.error("❌ Không thể xóa dữ liệu")
            return False

        time.sleep(3)

        if not self.open_tiktok():
            logger.error("❌ Không thể mở TikTok")
            return False

        # Chờ app load
        logger.info(f"⏳ Chờ app load ({self.delays['app_load']}s)...")
        time.sleep(self.delays['app_load'])

        # Chụp ảnh ban đầu
        self.take_screenshot("step1_app_loaded.png")

        # Các bước tap cơ bản
        steps = [
            ("Agree and continue", self.coords['agree']),
            ("Skip", self.coords['skip']),
            ("Profile tab", self.coords['profile']),
            ("Sign up", self.coords['signup'])
        ]

        for step_name, (x, y) in steps:
            logger.info(f"📍 Bước: {step_name}")
            self.tap(x, y)

            if step_name == "Skip":
                logger.info(f"⏳ Chờ sau skip ({self.delays['after_skip']}s)...")
                time.sleep(self.delays['after_skip'])
                self.take_screenshot("after_skip.png")
            elif step_name == "Sign up":
                time.sleep(self.delays['general'])
                self.take_screenshot("signup_screen.png")
            else:
                time.sleep(self.delays['general'])

        # Bây giờ tìm nút Google
        logger.info("🔍 Tìm nút 'Continue with Google'...")
        self.find_google_button()

        logger.info("✅ Hoàn thành! Kiểm tra ảnh để xem kết quả")
        return True
    
    def register_with_account(self, username, password):
        """Đăng ký với username/password"""
        logger.info(f"🎯 Đăng ký tài khoản: {username}")
        
        if not self.run_basic_flow():
            return False
        
        # Tap vào ô phone và nhập thông tin
        time.sleep(5)
        self.tap(self.coords['phone'][0], self.coords['phone'][1])
        time.sleep(2)
        
        self.input_text(username)
        time.sleep(3)
        
        # Tap Next (tọa độ ước tính)
        self.tap(540, 1400)
        time.sleep(3)
        
        self.input_text(password)
        time.sleep(3)
        
        # Tap Sign up
        self.tap(540, 1600)
        
        logger.info("✅ Hoàn thành đăng ký!")
        return True

def print_banner():
    print("=" * 50)
    print("🔧 TikTok Automation - J7 Prime (Đơn giản)")
    print("=" * 50)
    print("📱 Thiết bị: Samsung Galaxy J7 Prime")
    print("⚡ Chế độ: Tối ưu cho lag")
    print("=" * 50)

def main():
    print_banner()
    
    # Kiểm tra chuẩn bị
    print("\n✅ Checklist:")
    print("- USB Debugging đã bật")
    print("- Kết nối USB ổn định") 
    print("- TikTok đã cài đặt")
    print("- Đóng tất cả app khác")
    print("- Sạc pin > 50%")
    
    input("\n📱 Nhấn Enter khi đã sẵn sàng...")
    
    automation = TikTokSimple()
    
    print("\n📋 Chọn chế độ:")
    print("1. 🧠 THÔNG MINH - Tự động tìm và click (KHUYẾN NGHỊ)")
    print("2. 🐌 Cơ bản - Delay cứng (chậm)")
    print("3. 📝 Đăng ký với username/password")
    print("4. 🔐 AUTO VERIFICATION - Tự động đọc mã SMS/Gmail")
    print("5. 📸 Chụp ảnh màn hình hiện tại")
    print("6. 🚪 Thoát")

    choice = input("\nNhập lựa chọn (1-5): ").strip()
    
    if choice == "1":
        print("🧠 Chạy chế độ THÔNG MINH...")
        automation.run_smart_flow()

    elif choice == "2":
        print("🐌 Chạy chế độ cơ bản...")
        automation.run_basic_flow()

    elif choice == "3":
        username = input("Nhập username/email/phone: ").strip()
        password = input("Nhập password: ").strip()

        if username and password:
            automation.register_with_account(username, password)
        else:
            print("❌ Vui lòng nhập đầy đủ thông tin")

    elif choice == "4":
        print("📸 Chụp ảnh màn hình hiện tại...")
        automation.take_screenshot("current_screen.png")
        print("✅ Đã chụp ảnh: current_screen.png")

    elif choice == "5":
        print("👋 Tạm biệt!")

    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
